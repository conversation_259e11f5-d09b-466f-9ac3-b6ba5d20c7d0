version: '3.8'

services:
  tender-api:
    build: .
    ports:
      - "8000:8000"
    environment:
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - OPENAI_MODEL=${OPENAI_MODEL:-gpt-3.5-turbo}
      - MAX_FILE_SIZE=${MAX_FILE_SIZE:-314572800}
      - TIMEOUT_SECONDS=${TIMEOUT_SECONDS:-30}
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
      - TEMP_DIR=temp
    volumes:
      - ./temp:/app/temp
      - ./logs:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/v1/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # 可选：添加nginx反向代理
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
    depends_on:
      - tender-api
    restart: unless-stopped
    profiles:
      - production
