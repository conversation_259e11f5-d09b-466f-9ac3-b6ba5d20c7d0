# 招标文件关键信息自动提取API接口需求文档

## 项目简介

基于现有的招标文件合规性检查助手项目架构，开发一个最小化的招标文件关键信息自动提取API接口。该接口专注于核心功能，从招标文件中自动提取13个关键字段信息，并以结构化JSON格式返回。

## 需求

### 需求 1

**用户故事：** 作为招投标业务人员，我希望通过API接口上传招标文件URL，系统能够自动提取关键信息，以便快速获取结构化的招标数据。

#### 验收标准

1. 当用户提供有效的招标文件URL时，系统应当能够成功下载并解析文件内容
2. 当文件解析完成后，系统应当返回包含13个关键字段的JSON格式数据
3. 当文件URL无效或无法访问时，系统应当返回相应的错误信息
4. 当文件格式不支持时，系统应当返回格式不支持的错误提示

### 需求 2

**用户故事：** 作为系统集成人员，我希望API接口能够处理指定格式的招标文件，以便支持标准的文档处理需求。

#### 验收标准

1. 当上传PDF格式的招标文件时，系统应当能够提取文本内容并解析关键信息
2. 当上传DOCX格式的招标文件时，系统应当能够提取文本内容并解析关键信息
3. 当文件格式不是PDF或DOCX时，系统应当返回格式不支持的错误提示
4. 当文件URL无效时，系统应当返回相应的错误信息

### 需求 3

**用户故事：** 作为数据分析人员，我希望系统能够智能识别招标文件的关键章节并从中提取准确的信息，以便提高处理效率和准确性。

#### 验收标准

1. 当系统提取文件内容后，应当识别并提取第三章投标人须知之前的内容（封面、目录、第一章招标公告、第二章投标须知前附表）
2. 当识别到第三章投标人须知标题时，系统应当停止内容提取，只分析前面的章节内容
3. 当无法识别章节结构时，系统应当提取文件前30%的内容进行分析
4. 系统应当基于提取的关键章节内容进行信息解析，而不是全文分析

### 需求 4

**用户故事：** 作为数据分析人员，我希望从关键章节中提取的信息字段完整准确，以便进行后续的数据分析和处理。

#### 验收标准

1. 当招标文件包含项目名称信息时，系统应当准确提取并填入prj_name字段
2. 当招标文件包含项目编号信息时，系统应当准确提取并填入prj_number字段
3. 当招标文件包含业主单位信息时，系统应当准确提取招标人/采购人名称并填入tenderee字段
4. 当招标文件包含联系人信息时，系统应当准确提取联系人姓名、电话、地址信息
5. 当招标文件包含标包划分信息时，系统应当准确提取并填入bidding_packages_division字段
6. 当招标文件包含时间信息时，系统应当准确提取并转换为yyyy-MM-dd HH:mm:ss格式
7. 当招标文件包含地点信息时，系统应当准确提取开标地点信息
8. 当招标文件包含金额信息时，系统应当准确提取并统一转换为人民币元单位
9. 当某个字段信息在文件中不存在时，系统应当将该字段值设置为null
10. 当某个字段信息不确定时，系统应当将该字段值设置为null

### 需求 5

**用户故事：** 作为API调用方，我希望接口具有基本的错误处理机制，以便能够处理常见的异常情况。

#### 验收标准

1. 当API接收到无效请求参数时，系统应当返回400状态码和错误信息
2. 当文件下载失败时，系统应当返回相应的错误信息
3. 当API调用成功时，系统应当返回200状态码和JSON数据
4. 当文件解析失败时，系统应当返回500状态码和错误信息