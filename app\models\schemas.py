"""
数据模型定义
包含请求响应模型和招标信息数据结构
"""

from typing import Optional, Dict, Any
from pydantic import BaseModel, Field, HttpUrl, ConfigDict


class ExtractionRequest(BaseModel):
    """信息提取请求模型"""

    file_url: HttpUrl = Field(..., description="招标文件URL")

    model_config = ConfigDict(
        json_schema_extra={
            "example": {"file_url": "https://example.com/tender_document.pdf"}
        }
    )


class TenderInfo(BaseModel):
    """招标信息数据结构 - 包含13个关键字段"""

    prj_name: Optional[str] = Field(None, description="招标项目名称或者采购项目名称")
    prj_number: Optional[str] = Field(None, description="招标项目编号或者采购项目编号")
    tenderee: Optional[str] = Field(
        None, description="招标人名称或者招标单位名称或者采购人名称或者采购单位名称"
    )
    tenderee_contact_person: Optional[str] = Field(None, description="招标人联系人姓名")
    tenderee_contact_phone_number: Optional[str] = Field(
        None, description="招标人联系电话"
    )
    tenderee_contact_addr: Optional[str] = Field(None, description="招标人地址")
    bidding_packages_division: Optional[str] = Field(
        None, description="标包（或者标段或者包别）的划分情况"
    )
    bidding_docs_obtain_time: Optional[str] = Field(
        None, description="招标采购文件的获取时间 (yyyy-MM-dd HH:mm:ss)"
    )
    tender_docs_submission_deadline: Optional[str] = Field(
        None, description="投标文件递交的截止时间 (yyyy-MM-dd HH:mm:ss)"
    )
    bid_open_time: Optional[str] = Field(
        None, description="开标时间 (yyyy-MM-dd HH:mm:ss)"
    )
    bid_open_location: Optional[str] = Field(None, description="开标地点")
    bid_guarantee: Optional[float] = Field(None, description="投标保证金 (人民币元)")
    highest_bid_limit: Optional[float] = Field(
        None, description="最高投标限价或者控制价 (人民币元)"
    )

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "prj_name": "某市政道路建设项目",
                "prj_number": "ZBTB-2023-001",
                "tenderee": "某市建设局",
                "tenderee_contact_person": "张三",
                "tenderee_contact_phone_number": "010-12345678",
                "tenderee_contact_addr": "北京市朝阳区某街道123号",
                "bidding_packages_division": "本项目不分标段，为一个整体标包",
                "bidding_docs_obtain_time": "2023-12-01 09:00:00",
                "tender_docs_submission_deadline": "2023-12-15 16:00:00",
                "bid_open_time": "2023-12-15 16:30:00",
                "bid_open_location": "某市公共资源交易中心第一开标室",
                "bid_guarantee": 100000.0,
                "highest_bid_limit": 5000000.0,
            }
        }
    )


class ExtractionResponse(BaseModel):
    """信息提取响应模型"""

    success: bool = Field(..., description="是否成功")
    data: Optional[TenderInfo] = Field(None, description="提取的招标信息")
    error: Optional[str] = Field(None, description="错误信息")

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "success": True,
                "data": {
                    "prj_name": "某市政道路建设项目",
                    "prj_number": "ZBTB-2023-001",
                    "tenderee": "某市建设局",
                },
                "error": None,
            }
        }
    )


class ErrorResponse(BaseModel):
    """错误响应模型"""

    success: bool = Field(False, description="是否成功")
    error: str = Field(..., description="错误描述信息")
    code: Optional[str] = Field(None, description="错误代码")

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "success": False,
                "error": "文件下载失败",
                "code": "FILE_DOWNLOAD_ERROR",
            }
        }
    )
