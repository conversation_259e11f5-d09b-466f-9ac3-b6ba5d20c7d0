"""
章节识别器
实现智能章节识别算法，提取关键章节内容
"""

import re
from typing import Optional, Tuple


class ChapterRecognizer:
    """章节识别器"""

    def __init__(self):
        # 第三章投标人须知的识别模式
        self.chapter_three_patterns = [
            r"第三章.*?投标人须知",
            r"三[、\s].*?投标人须知",  # 修改为支持 "三、" 和 "三 " 格式
            r"第\s*3\s*章.*?投标人须知",
            r"3\s*[、.．]\s*投标人须知",
            r"3\.\s*投标人须知",  # 添加对 "3. 投标人须知" 的支持
            r"投标人须知.*?第三章",
            r"投标人须知.*?三",
        ]

        # 编译正则表达式模式
        self.compiled_patterns = [
            re.compile(pattern, re.IGNORECASE | re.DOTALL)
            for pattern in self.chapter_three_patterns
        ]

    def extract_key_sections(self, content: str) -> str:
        """
        提取关键章节内容

        识别并提取关键章节：
        - 封面
        - 目录
        - 第一章招标公告
        - 第二章投标须知前附表

        Args:
            content: 文档全文内容

        Returns:
            str: 提取的关键章节内容
        """
        # 首先尝试找到第三章投标人须知的边界
        boundary_position = self.find_chapter_boundary(content)

        if boundary_position is not None:
            # 找到边界，提取边界之前的内容
            key_content = content[:boundary_position].strip()

            # 如果提取的内容太短，使用备用方案
            if len(key_content) < 500:  # 如果内容少于500字符，可能识别有误
                return self.extract_first_30_percent(content)

            return key_content
        else:
            # 无法识别章节结构，使用备用方案
            return self.extract_first_30_percent(content)

    def find_chapter_boundary(self, content: str) -> Optional[int]:
        """
        查找第三章投标人须知的位置

        Args:
            content: 文档内容

        Returns:
            Optional[int]: 第三章开始的位置，如果未找到返回None
        """
        # 尝试各种模式匹配
        for pattern in self.compiled_patterns:
            match = pattern.search(content)
            if match:
                # 检查是否确实是第三章（不是第四章等）
                matched_text = match.group()
                if re.search(r"第\s*三\s*章|三\s*[、.]", matched_text, re.IGNORECASE):
                    return match.start()

        # 尝试更严格的第三章匹配
        strict_patterns = [
            r"第\s*三\s*章.*?投标人须知",
            r"三\s*[、.\s]\s*投标人须知",  # 支持 "三、"、"三." 和 "三 " 格式
            r"第\s*3\s*章.*?投标人须知",
            r"3\.\s*投标人须知",  # 添加对 "3. 投标人须知" 的支持
        ]

        for pattern in strict_patterns:
            match = re.search(pattern, content, re.IGNORECASE)
            if match:
                return match.start()

        # 更宽松的匹配，但要排除"前附表"
        loose_pattern = r"投标人须知(?!前附表)"
        matches = list(re.finditer(loose_pattern, content, re.IGNORECASE))

        if matches:
            content_length = len(content)
            for match in matches:
                # 检查前后文，确保是第三章
                start_pos = max(0, match.start() - 20)
                end_pos = min(len(content), match.end() + 20)
                context = content[start_pos:end_pos]

                # 如果上下文中包含"第三章"或"三、"等标识
                if re.search(r"第\s*三\s*章|三\s*[、.]", context, re.IGNORECASE):
                    return match.start()

                # 如果匹配位置在文档的后半部分，可能是第三章
                if match.start() > content_length * 0.5:
                    return match.start()

        return None

    def extract_first_30_percent(self, content: str) -> str:
        """
        备用方案：提取前30%内容

        Args:
            content: 文档内容

        Returns:
            str: 前30%的内容
        """
        content_length = len(content)
        thirty_percent_length = int(content_length * 0.3)

        # 提取前30%内容
        extracted_content = content[:thirty_percent_length]

        # 尝试在合适的位置截断（避免在句子中间截断）
        # 寻找最后一个句号、问号或感叹号
        sentence_endings = [".", "。", "!", "！", "?", "？"]

        best_cut_position = thirty_percent_length
        for i in range(
            thirty_percent_length - 1, max(0, thirty_percent_length - 200), -1
        ):
            if extracted_content[i] in sentence_endings:
                best_cut_position = i + 1
                break

        return extracted_content[:best_cut_position].strip()

    def analyze_document_structure(self, content: str) -> dict:
        """
        分析文档结构

        Args:
            content: 文档内容

        Returns:
            dict: 文档结构分析结果
        """
        analysis = {
            "total_length": len(content),
            "has_chapter_three": False,
            "chapter_three_position": None,
            "extraction_method": "percentage",  # 'boundary' 或 'percentage'
            "key_sections_found": [],
        }

        # 查找第三章位置
        boundary_position = self.find_chapter_boundary(content)
        if boundary_position is not None:
            analysis["has_chapter_three"] = True
            analysis["chapter_three_position"] = boundary_position
            analysis["extraction_method"] = "boundary"

        # 识别可能的关键章节
        section_patterns = {
            "招标公告": r"招标公告|招标通告",
            "投标须知前附表": r"投标须知前附表|投标人须知前附表",
            "目录": r"目\s*录|contents",
            "项目概况": r"项目概况|工程概况",
        }

        for section_name, pattern in section_patterns.items():
            if re.search(pattern, content, re.IGNORECASE):
                analysis["key_sections_found"].append(section_name)

        return analysis

    def get_extraction_summary(
        self, original_content: str, extracted_content: str
    ) -> dict:
        """
        获取提取摘要信息

        Args:
            original_content: 原始内容
            extracted_content: 提取的内容

        Returns:
            dict: 提取摘要
        """
        original_length = len(original_content)
        extracted_length = len(extracted_content)

        return {
            "original_length": original_length,
            "extracted_length": extracted_length,
            "extraction_ratio": (
                extracted_length / original_length if original_length > 0 else 0
            ),
            "extraction_method": (
                "boundary"
                if self.find_chapter_boundary(original_content)
                else "percentage"
            ),
        }
