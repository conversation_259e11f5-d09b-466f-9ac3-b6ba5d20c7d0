# 招标文件关键信息自动提取API

基于FastAPI的招标文件关键信息自动提取API接口，支持从PDF和DOCX格式的招标文件中自动提取13个关键字段信息。

## 功能特性

- 支持PDF和DOCX格式文件解析
- 智能章节识别，提取关键信息章节
- 基于LLM的信息提取
- 完整的错误处理和日志记录
- RESTful API接口
- Docker容器化部署
- 完整的单元测试和集成测试

## 提取字段

系统可以提取以下13个关键字段：

1. `prj_name` - 招标项目名称
2. `prj_number` - 招标项目编号
3. `tenderee` - 招标人名称
4. `tenderee_contact_person` - 招标人联系人
5. `tenderee_contact_phone_number` - 招标人联系电话
6. `tenderee_contact_addr` - 招标人地址
7. `bidding_packages_division` - 标包划分情况
8. `bidding_docs_obtain_time` - 招标文件获取时间
9. `tender_docs_submission_deadline` - 投标文件递交截止时间
10. `bid_open_time` - 开标时间
11. `bid_open_location` - 开标地点
12. `bid_guarantee` - 投标保证金（人民币元）
13. `highest_bid_limit` - 最高投标限价（人民币元）

## 快速开始

### 环境要求

- Python 3.9+
- OpenAI API密钥

### 本地开发

1. 克隆项目
```bash
git clone <repository-url>
cd bidding_doc_key_info_extract
```

2. 安装依赖
```bash
pip install -r requirements.txt
```

3. 配置环境变量
```bash
cp .env.example .env
# 编辑.env文件，设置OPENAI_API_KEY
```

4. 启动服务
```bash
python main.py
```

服务将在 http://localhost:8089 启动

### Docker部署

1. 构建镜像
```bash
docker build -t tender-api .
```

2. 运行容器
```bash
docker run -d \
  -p 8000:8000 \
  -e OPENAI_API_KEY=your_api_key \
  -v $(pwd)/temp:/app/temp \
  -v $(pwd)/logs:/app/logs \
  tender-api
```

### Docker Compose部署

1. 配置环境变量
```bash
cp .env.example .env
# 编辑.env文件
```

2. 启动服务
```bash
docker-compose up -d
```

## API使用

### 提取招标信息

**请求**
```http
POST /api/v1/extract
Content-Type: application/json

{
    "file_url": "https://example.com/tender_document.pdf"
}
```

**响应**
```json
{
    "success": true,
    "data": {
        "prj_name": "某市政道路建设项目",
        "prj_number": "ZBTB-2023-001",
        "tenderee": "某市建设局",
        "tenderee_contact_person": "张三",
        "tenderee_contact_phone_number": "010-12345678",
        "tenderee_contact_addr": "北京市朝阳区某街道123号",
        "bidding_packages_division": "本项目不分标段，为一个整体标包",
        "bidding_docs_obtain_time": "2023-12-01 09:00:00",
        "tender_docs_submission_deadline": "2023-12-15 16:00:00",
        "bid_open_time": "2023-12-15 16:30:00",
        "bid_open_location": "某市公共资源交易中心第一开标室",
        "bid_guarantee": 100000.0,
        "highest_bid_limit": 5000000.0
    },
    "error": null
}
```

### 健康检查

```http
GET /api/v1/health
```

## 测试

### 运行单元测试
```bash
pytest tests/ -v
```

### 运行集成测试
```bash
pytest tests/test_integration.py -v -m integration
```

### 运行所有测试
```bash
pytest
```

## 配置说明

### 环境变量

- `OPENAI_API_KEY`: OpenAI API密钥（必需）
- `OPENAI_MODEL`: 使用的模型，默认为gpt-3.5-turbo
- `MAX_FILE_SIZE`: 最大文件大小限制，默认300MB
- `TIMEOUT_SECONDS`: 请求超时时间，默认30秒
- `LOG_LEVEL`: 日志级别，默认INFO

### 文件限制

- 支持格式：PDF、DOCX
- 最大文件大小：300MB
- 下载超时：30秒

## 错误处理

API返回标准的错误响应格式：

```json
{
    "success": false,
    "error": "错误描述信息",
    "code": "ERROR_CODE"
}
```

常见错误代码：
- `FILE_DOWNLOAD_ERROR`: 文件下载失败
- `UNSUPPORTED_FORMAT_ERROR`: 不支持的文件格式
- `PARSE_ERROR`: 文档解析失败
- `EXTRACTION_ERROR`: 信息提取失败
- `TIMEOUT_ERROR`: 请求超时
- `VALIDATION_ERROR`: 参数验证失败

## 项目结构

```
├── app/
│   ├── api/           # API路由
│   ├── core/          # 核心配置
│   ├── models/        # 数据模型
│   ├── services/      # 业务服务
│   └── utils/         # 工具函数
├── tests/             # 测试用例
├── temp/              # 临时文件目录
├── logs/              # 日志目录
├── main.py            # 应用入口
├── requirements.txt   # 依赖列表
├── Dockerfile         # Docker配置
└── docker-compose.yml # Docker Compose配置
```

## 许可证

[MIT License](LICENSE)
