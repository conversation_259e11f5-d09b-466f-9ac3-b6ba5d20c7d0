"""
应用配置管理
"""

import os
from typing import Optional
from pydantic_settings import BaseSettings
from pydantic import ConfigDict


class Settings(BaseSettings):
    """应用配置类"""

    # API配置
    API_V1_STR: str = "/api/v1"
    PROJECT_NAME: str = "招标文件关键信息自动提取API"

    # OpenAI配置
    OPENAI_API_KEY: Optional[str] = None
    OPENAI_MODEL: str = "gpt-3.5-turbo"

    # 文件处理配置
    MAX_FILE_SIZE: int = 300 * 1024 * 1024  # 300MB (按设计文档要求)
    TIMEOUT_SECONDS: int = 30
    TEMP_DIR: str = "temp"

    # 日志配置
    LOG_LEVEL: str = "INFO"

    model_config = ConfigDict(case_sensitive=True, env_file=".env")


settings = Settings()
