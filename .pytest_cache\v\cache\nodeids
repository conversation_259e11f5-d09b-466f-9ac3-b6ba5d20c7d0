["tests/test_chapter_recognizer.py::TestChapterRecognizer::test_analyze_document_structure", "tests/test_chapter_recognizer.py::TestChapterRecognizer::test_boundary_detection_cases[\\u4e09\\u3001\\u6295\\u6807\\u4eba\\u987b\\u77e5-True]", "tests/test_chapter_recognizer.py::TestChapterRecognizer::test_boundary_detection_cases[\\u6295\\u6807\\u4eba\\u987b\\u77e5\\u524d\\u9644\\u8868-False]", "tests/test_chapter_recognizer.py::TestChapterRecognizer::test_boundary_detection_cases[\\u666e\\u901a\\u6587\\u672c-False]", "tests/test_chapter_recognizer.py::TestChapterRecognizer::test_boundary_detection_cases[\\u7b2c\\u4e09\\u7ae0\\u6295\\u6807\\u4eba\\u987b\\u77e5-True]", "tests/test_chapter_recognizer.py::TestChapterRecognizer::test_boundary_detection_cases[\\u7b2c\\u56db\\u7ae0\\u6295\\u6807\\u4eba\\u987b\\u77e5-False]", "tests/test_chapter_recognizer.py::TestChapterRecognizer::test_extract_first_30_percent", "tests/test_chapter_recognizer.py::TestChapterRecognizer::test_extract_first_30_percent_sentence_boundary", "tests/test_chapter_recognizer.py::TestChapterRecognizer::test_extract_key_sections_short_content", "tests/test_chapter_recognizer.py::TestChapterRecognizer::test_extract_key_sections_with_boundary", "tests/test_chapter_recognizer.py::TestChapterRecognizer::test_extract_key_sections_without_boundary", "tests/test_chapter_recognizer.py::TestChapterRecognizer::test_find_chapter_boundary_basic", "tests/test_chapter_recognizer.py::TestChapterRecognizer::test_find_chapter_boundary_not_found", "tests/test_chapter_recognizer.py::TestChapterRecognizer::test_find_chapter_boundary_variations", "tests/test_chapter_recognizer.py::TestChapterRecognizer::test_get_extraction_summary"]