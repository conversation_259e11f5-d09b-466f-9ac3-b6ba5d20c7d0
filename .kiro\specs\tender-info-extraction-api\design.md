# 招标文件关键信息自动提取API接口设计文档

## 概述

本设计文档描述了招标文件关键信息自动提取API接口的技术架构和实现方案。该系统采用最小化设计原则，专注于从PDF和DOCX格式的招标文件中提取13个关键字段信息，并通过智能章节识别技术提高处理效率。

## 架构设计

### 系统架构图

```mermaid
graph TB
    A[客户端] --> B[API网关]
    B --> C[文件下载服务]
    B --> D[文档解析服务]
    B --> E[信息提取服务]
    
    C --> F[文件存储]
    D --> G[文本提取器]
    G --> H[PDF解析器]
    G --> I[DOCX解析器]
    
    D --> J[章节识别器]
    E --> K[LLM信息提取器]
    E --> L[数据格式化器]
    
    subgraph "核心组件"
        G
        J
        K
        L
    end
```

### 技术栈选择

- **Web框架**: FastAPI (高性能、自动文档生成)
- **文档解析**: PyPDF2/pdfplumber (PDF), python-docx (DOCX)
- **LLM集成**: OpenAI API 或其他兼容接口
- **HTTP客户端**: httpx (异步文件下载)
- **数据验证**: Pydantic (请求/响应模型)
- **日志记录**: Python logging
- **部署**: Docker容器化

## 组件设计

### 1. API接口层

#### 1.1 请求模型
```python
class ExtractionRequest(BaseModel):
    file_url: str = Field(..., description="招标文件URL")
    
class ExtractionResponse(BaseModel):
    success: bool
    data: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
```

#### 1.2 API端点
- `POST /api/v1/extract` - 主要提取接口

### 2. 文件处理服务

#### 2.1 文件下载器
- 支持HTTP/HTTPS协议
- 文件大小限制：50MB
- 超时设置：30秒
- 支持的MIME类型：application/pdf, application/vnd.openxmlformats-officedocument.wordprocessingml.document

#### 2.2 文档解析器
```python
class DocumentParser:
    def parse_pdf(self, file_path: str) -> str
    def parse_docx(self, file_path: str) -> str
    def detect_format(self, file_path: str) -> str
```

### 3. 章节识别服务

#### 3.1 智能章节识别
```python
class ChapterRecognizer:
    def extract_key_sections(self, content: str) -> str:
        """
        识别并提取关键章节：
        - 封面
        - 目录  
        - 第一章招标公告
        - 第二章投标须知前附表
        """
        
    def find_chapter_boundary(self, content: str) -> int:
        """查找第三章投标人须知的位置"""
        
    def extract_first_30_percent(self, content: str) -> str:
        """备用方案：提取前30%内容"""
```

#### 3.2 章节识别规则
- 关键词匹配：`第三章.*投标人须知`、`三.*投标人须知`
- 正则表达式：`[第三3].*[章节].*投标人须知`
- 备用策略：如无法识别章节，提取前30%内容

### 4. 信息提取服务

#### 4.1 LLM提取器
```python
class LLMExtractor:
    def __init__(self, api_key: str, model: str = "gpt-3.5-turbo"):
        self.client = OpenAI(api_key=api_key)
        self.model = model
        
    def extract_info(self, content: str) -> Dict[str, Any]:
        """使用预定义提示词模板提取信息"""
```

#### 4.2 提示词模板
使用需求中提供的标准提示词模板，包含：
- 角色定义：招投标行业专家
- 输出格式：JSON格式
- 字段定义：13个关键字段的详细说明
- 处理规则：null值处理、金额单位转换等

#### 4.3 数据字段映射
```python
FIELD_MAPPING = {
    'prj_name': '招标项目名称或者采购项目名称',
    'prj_number': '招标项目编号或者采购项目编号', 
    'tenderee': '招标人名称或者招标单位名称或者采购人名称或者采购单位名称',
    'tenderee_contact_person': '招标人联系人姓名',
    'tenderee_contact_phone_number': '招标人联系电话',
    'tenderee_contact_addr': '招标人地址',
    'bidding_packages_division': '标包（或者标段或者包别）的划分情况',
    'bidding_docs_obtain_time': '招标采购文件的获取时间',
    'tender_docs_submission_deadline': '投标文件递交的截止时间',
    'bid_open_time': '开标时间',
    'bid_open_location': '开标地点',
    'bid_guarantee': '投标保证金',
    'highest_bid_limit': '最高投标限价或者控制价'
}
```

## 数据模型

### 响应数据结构
```python
class TenderInfo(BaseModel):
    prj_name: Optional[str] = None
    prj_number: Optional[str] = None  
    tenderee: Optional[str] = None
    tenderee_contact_person: Optional[str] = None
    tenderee_contact_phone_number: Optional[str] = None
    tenderee_contact_addr: Optional[str] = None
    bidding_packages_division: Optional[str] = None
    bidding_docs_obtain_time: Optional[str] = None
    tender_docs_submission_deadline: Optional[str] = None
    bid_open_time: Optional[str] = None
    bid_open_location: Optional[str] = None
    bid_guarantee: Optional[float] = None
    highest_bid_limit: Optional[float] = None
```

## 错误处理

### 错误类型定义
```python
class APIError(Exception):
    def __init__(self, code: int, message: str):
        self.code = code
        self.message = message

class FileDownloadError(APIError): pass
class UnsupportedFormatError(APIError): pass  
class ParseError(APIError): pass
class ExtractionError(APIError): pass
```

### 错误响应格式
```json
{
    "success": false,
    "error": "错误描述信息",
    "code": "ERROR_CODE"
}
```

### 状态码映射
- 200: 成功
- 400: 请求参数错误
- 415: 不支持的文件格式
- 500: 服务器内部错误

## 测试策略

### 单元测试
- 文档解析器测试
- 章节识别器测试  
- 信息提取器测试
- 数据验证测试

### 集成测试
- API端点测试
- 完整流程测试
- 错误场景测试

### 测试数据
- 标准PDF招标文件样本
- 标准DOCX招标文件样本
- 异常格式文件样本
- 无效URL测试用例

## 性能优化

### 处理效率优化
1. **智能章节识别**: 只处理前几章内容，减少LLM处理量
2. **文件大小限制**: 限制50MB以内文件
3. **超时控制**: 设置合理的超时时间
4. **缓存机制**: 对相同文件URL进行结果缓存（可选）

### 资源管理
- 临时文件自动清理
- 内存使用监控
- 并发请求限制

## 部署方案

### Docker容器化
```dockerfile
FROM python:3.9-slim
WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt
COPY . .
EXPOSE 8000
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
```

### 环境变量配置
- `OPENAI_API_KEY`: LLM API密钥
- `MAX_FILE_SIZE`: 最大文件大小限制
- `TIMEOUT_SECONDS`: 请求超时时间
- `LOG_LEVEL`: 日志级别